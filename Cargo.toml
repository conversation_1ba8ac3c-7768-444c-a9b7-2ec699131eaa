[package]
name = "dima-ai-agent-v2"
version = "0.1.0"
edition = "2021"

[dependencies]
colored = "2.1.0"
# crossterm and reedline versions need to be compatible
crossterm = { version = "0.28.1", features = ["event-stream"] }
reedline = { version = "0.40.0" }
arboard = "3.4"
tokio = { version = "1.35.1", features = ["full"] }
reqwest = { version = "0.12.20", features = ["json", "rustls-tls", "blocking"], default-features = false }
serde_json = "1.0.111"
thiserror = "2.0.12"
clap = { version = "4.5.4", features = ["derive"] }
itertools = "0.14.0"
llm = { version = "1.3.1", path = "llm" }
termimad = "0.33.0"
regex = "1.10.3"
nu-ansi-term = "0.50.1"
enum-iterator = "2.1.0"
console = "0.16.0"
serde = { version = "1.0", features = ["derive"] }
chrono = { version = "0.4", features = ["serde"] }
dirs = "6.0.0"
seahash = "4.1.0"
inquire = "0.7.5"
tempfile = "3.20.0"
dialoguer = "0.11.0"
num-format = "0.4.4"
