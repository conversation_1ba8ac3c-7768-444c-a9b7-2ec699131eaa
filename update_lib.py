#!/usr/bin/env python3

# Read the original file
with open('llm/src/lib.rs', 'r') as f:
    lines = f.readlines()

# Find the line with "/// Core trait that all LLM providers"
insert_index = None
for i, line in enumerate(lines):
    if '/// Core trait that all LLM providers' in line:
        insert_index = i
        break

if insert_index is None:
    print("Could not find insertion point")
    exit(1)

# Insert the new struct
new_lines = [
    "\n",
    "/// GitHub Copilot usage information\n",
    "#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]\n",
    "pub struct CopilotUsageInfo {\n",
    "    pub chat_messages_left_per_month: i64,\n",
    "    pub completions_left_per_month: i64,\n",
    "    pub reset_date: String,\n",
    "    pub time_remaining: String,\n",
    "}\n",
    "\n"
]

# Insert the new lines
lines[insert_index:insert_index] = new_lines

# Write the modified file
with open('llm/src/lib.rs', 'w') as f:
    f.writelines(lines)

print("Successfully added CopilotUsageInfo struct")
