use std::io::{self, Write};
use std::path::PathBuf;
use std::process::Command;

fn main() -> io::Result<()> {
    let mut markdown_file = PathBuf::from(env!("CARGO_MANIFEST_DIR"));
    markdown_file.push("examples/markdown_example.md");

    println!(
        "--- Invoking mdcat to display '{}' ---\n",
        markdown_file.display()
    );

    let output = Command::new("mdcat")
        .arg("--columns")
        .arg("80")
        .arg(markdown_file) // The file to display
        .output(); // Execute and capture the output

    match output {
        Ok(output) => {
            if output.status.success() {
                // The command was successful.
                // `output.stdout` is a Vec<u8> containing the colored output.
                // We write these bytes directly to our own program's stdout.
                io::stdout().write_all(&output.stdout)?;
            } else {
                // The command failed. Print the error message from stderr.
                eprintln!("`mdcat` command failed:");
                io::stderr().write_all(&output.stderr)?;
            }
        }
        Err(e) => {
            // This error means the command could not be spawned.
            // Most likely, `mdcat` is not installed or not in the system's PATH.
            eprintln!("Failed to execute `mdcat`: {e}");
            eprintln!("Please ensure `mdcat` is installed and accessible in your PATH.");
        }
    }

    Ok(())
}
