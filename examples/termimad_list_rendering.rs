use std::sync::LazyLock;
use termimad::crossterm::style::Attribute;
use termimad::crossterm::style::Color::{Blue, White, Yellow};
use termimad::{
    gray, Alignment, CompoundStyle, FmtText, LineStyle, MadSkin, ScrollBarStyle, StyledChar,
    STANDARD_TABLE_BORDER_CHARS,
};

const MARGIN: usize = 0;
const DISPLAY_WIDTH: usize = 90;

static SKIN: LazyLock<MadSkin> = LazyLock::new(|| {
    let mut skin = MadSkin {
        paragraph: LineStyle::default(),
        bold: CompoundStyle::default(),
        italic: CompoundStyle::with_attr(Attribute::Italic),
        strikeout: CompoundStyle::with_attr(Attribute::CrossedOut),
        inline_code: CompoundStyle::with_fg(Blue),
        code_block: LineStyle {
            compound_style: CompoundStyle::default(),
            ..Default::default()
        },
        headers: Default::default(),
        scrollbar: ScrollBarStyle::new(),
        table: CompoundStyle::with_fg(gray(7)).into(),
        bullet: StyledChar::from_fg_char(Yellow, '•'),
        quote_mark: StyledChar::new(
            CompoundStyle::new(Some(gray(12)), None, Attribute::Bold.into()),
            '▐',
        ),
        horizontal_rule: StyledChar::nude('─'),
        ellipsis: CompoundStyle::default(),
        table_border_chars: STANDARD_TABLE_BORDER_CHARS,
        list_items_indentation_mode: Default::default(),
        special_chars: Default::default(),
    };
    skin.set_headers_fg(White);
    skin.paragraph.left_margin = MARGIN;
    skin.paragraph.right_margin = MARGIN;
    skin.code_block.left_margin = MARGIN;
    skin.code_block.right_margin = MARGIN;
    skin.table.left_margin = MARGIN;
    skin.table.right_margin = MARGIN;
    skin.code_block.left_margin = MARGIN;
    for header_style in &mut skin.headers {
        header_style.left_margin = MARGIN;
        header_style.right_margin = MARGIN;
        header_style.align = Alignment::Center;
        // unset the default skin underline
        header_style.add_attr(Attribute::NoUnderline);
    }
    skin
});

fn main() {
    let text = "# Hello, Termimad!

2.  src/dot_commands/name_command.rs:
*   Added `use crossterm::{execute, terminal::SetTitle};` for `crossterm` functionality.
*   Integrated code within the `handle_name_command` function to update the terminal title after a session is successfully renamed. This ensures that the terminal tab title remains consistent with the current session's working directory. Error handling for `SetTitle` execution is also included here.
";

    let (width, _) = termimad::terminal_size();
    // Wrap, but not more than the physical terminal width.
    let wrap_width = if width == 0 {
        DISPLAY_WIDTH
    } else {
        (width as usize).min(DISPLAY_WIDTH)
    };
    let fmt_text = FmtText::from(&SKIN, text.trim(), Some(wrap_width));
    println!("{fmt_text}");
}
