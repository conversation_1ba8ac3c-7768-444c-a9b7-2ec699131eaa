# Improved Markdown Display

Welcome to the enhanced markdown display system!

## New Features

### 1. Enhanced Headings
- H1 headings now have beautiful borders
- Proper spacing before and after headings
- Centered text for better visual appeal

### 2. Improved Lists

#### Unordered Lists
- First item
- Second item
    - Nested item A
    - Nested item B
- Third item with `nested code`

#### Ordered Lists
1. First numbered item
2. Second numbered item
    1. Nested numbered item
    2. Another nested item
3. Third numbered item with `nested code`

### 3. Clickable Links
Check out these links:
- [Rust Language](https://rust-lang.org "The Rust Programming Language")
- [GitHub](https://github.com)
- [Documentation](https://doc.rust-lang.org/)
- Item with [link](https://example.com) inside

### 4. Beautiful Tables

| Feature | Status | Priority |
|---------|--------|----------|
| H1 Borders | ✅ Complete | High |
| Ordered Lists | ✅ Complete | High |
| Links | ✅ Complete | Medium |
| Tables | ✅ Complete | Medium |

| Left Aligned | Center Aligned | Right Aligned |
|:-------------|:--------------:|--------------:|
| Text | Centered | Numbers |
| More text | More centered | 123 |

### 5. Text Formatting
**bold text**, *italic text*, ~~strikethrough~~ and `inline code`

### 6. Code Blocks
```rust
fn main() {
    println!("Hello, enhanced markdown!");
    let features = vec!["borders", "tables", "links"];
    for feature in features {
        println!("✅ {}", feature);
    }
}
```

### 7. Newlines around lists

One

- First item
- Second item

Two

Three

1. First item
2. Second item **bold**, *italic*, `code`, ~~strikethrough~~

Four

### 8. Horizontal Ruler

---

### 9. Text wrapping

This is a very long sentence that will be used to test the word wrapping functionality of the bat markdown renderer. It contains many words and is designed to exceed the typical terminal width, forcing the renderer to break the text into multiple lines. We want to ensure that the wrapping is handled correctly and that the output remains readable and well-formatted.

### 10. Heading test

# h1
This is a short paragraph following an H1 heading. It should demonstrate how text wraps under this heading.
## h2
This is a short paragraph following an H2 heading. It should demonstrate how text wraps under this heading.
### h3
This is a short paragraph following an H3 heading. It should demonstrate how text wraps under this heading.
#### h4
This is a short paragraph following an H4 heading. It should demonstrate how text wraps under this heading.
##### h5
This is a short paragraph following an H5 heading. It should demonstrate how text wraps under this heading.
###### h6
This is a short paragraph following an H6 heading. It should demonstrate how text wraps under this heading.