# Dima AI agent

# Implementation notes

Heavily copied/inspired by Atlassian's Rovo Dev.

The goal is to have free providers only, but since the API keys are read from environment variables,
it is trivial to switch to paid providers.

This probably provides worse results because Rovo Dev supposedly uses Claude Sonnet 4, 3.7 or 3.5v2 which are tuned to
agentic workflows, and are the best for such workflows.

## Differences to Rovo Dev

- no streaming support implemented and likely not planned, I just don't really care about it
- different system prompts, but based on theirs
- no `expand_code_chunks` tool, I suppose RovoDev uses tree-sitter to construct it, a bit like `aider`
- `create_file` tool is renamed to `write_file` because I notice that many models mess up the `find_and_replace_code`
  tool, to force them to use it. The `find_and_replace_code` tool is optional
- my `grep_file_content` tool uses `ripgrep` to respect `.gitignore` and is always in `--ignore-case` mode. I have not
  verified how RovoDev does it

# Setup

Clone this with all submodules and then run `cargo install --path .`

The binary will be named `dima-ai-agent-v2`.

## Help

Run `dima-ai-agent-v2 --help` and run it, then you enter in `.help` or just enter in `.` which shows all available
commands.

## ripgrep (required)

https://github.com/BurntSushi/ripgrep

It is used because it respects `.gitignore`, and is pretty fast.
It is used with `--ignore-case`, so note that for huge code-bases.

It is required for the `grep_file_content` tool.
And for constructing a list of files in the working directories into the system prompt, named workspace.

## yek (optional)

https://github.com/bodo-run/yek

To copy all files in the working directory into the system prompt, the `yek` command is used.

## mdcat for better Markdown rendering (optional)

The `termimad` crate is used for rendering Markdown, but it has issues on long code blocks and long lines
where it breaks the line wrapping weirdly with more newlines.

I switched the default to the `mdcat` rendering, if you do not have it installed, specify:
`--markdown-render-engine termimad`.

---

For better rendering, you can switch rendering to the `mdcat` binary using the `--markdown-render-engine` flag:

`dima-ai-agent-v2 --markdown-render-engine mdcat`

`mdcat` is an optional binary dependency and can be installed from: https://github.com/lunaryorn/mdcat
or via macOS homebrew. If `mdcat`, is not installed, the `termimad` crate is used.

# Usage

Optional files to control the agent. They have to be in the root directory.

## dima-agent.json

```json
{
  "lintCommand": "cargo clippy"
}
```

## dima-agent.md

If it exists, the content is appended to the system prompt.

## Edit tools

By default, `OnlyWriteFile` is used which writes the whole file on any change. This can be overridden with
`--edit-tool`.
This leads to slower edits of files, but far more reliable results because weaker models are not capable of using the
`find_and_replace_code` tool correctly most of the time. Even stronger models like Gemini 2.0 Flash mess it up
frequently.

Note that with `OnlyWriteFile`, you should be careful to not have it edit huge files, as it would blow the output token
count of the model.

# Free API key providers

- https://github.com/cheahjs/free-llm-api-resources
- https://pollinations.ai/ (see https://text.pollinations.ai/models) - this does not even need an API key
- https://github.com/zukixa/cool-ai-stuff (many listed providers are highly limited in daily usage, so I don't use them)
- https://github.com/xtekky/gpt4free (no API key, runs a server on localhost with reverse engineered providers)

# Free agents

## Augment Code

https://www.augmentcode.com/pricing

Provides 50 free agent interactions per month.

It is excellent. Its context engine is also one of the RAG solutions out there.

## Gemini CLI

https://github.com/google-gemini/gemini-cli

It is fine, but it is slow. I find the UI annoying, and I often see Gemini 2.5/2.0 Flash messing up file edits.

But eventually, it gets the task done.

## Trae

https://www.trae.ai/

But the rate limits hit hard, so it is not so much fun to use.

## Gemini Code Assist

https://codeassist.google

Untested, has Jetbrains plugins.
Does it use the same model quote up, as the Gemini CLI?

# Model notes

Read `MODEL_NOTES.md` which contains info on my test runs with the models.

## Recommendations

- Google Gemini 2.5 Flash
- GitHub Copilot Claude 3.5 Sonnet
- Google Gemini 2.0 Flash (on the weaker side)
- Codestral (weak)

Claude 3.5 Sonnet is a very strong model, but Google has wonderful free rate limits on the Gemini models, so those are
preferred, although Gemini 2.0 Flash is not the best.

## About ripgrep

Because `rg` is used, you can include a `.rgignore` file in the root directory to ignore certain directories.
This is done in this repository for the `llm/` submodule.