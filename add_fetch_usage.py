#!/usr/bin/env python3

# Read the original file
with open('llm/src/backends/copilot.rs', 'r') as f:
    lines = f.readlines()

# Find the line with "// --- Token Caching ---"
insert_index = None
for i, line in enumerate(lines):
    if '// --- Token Caching ---' in line:
        insert_index = i
        break

if insert_index is None:
    print("Could not find insertion point")
    exit(1)

# Insert the new method
new_lines = [
    "\n",
    "    /// Fetches GitHub Copilot usage information from the API.\n",
    "    async fn fetch_copilot_usage(&self) -> Result<crate::CopilotUsageInfo, LLMError> {\n",
    "        let response = self\n",
    "            .client\n",
    "            .get(\"https://api.github.com/copilot_internal/v2/token\")\n",
    "            .header(\"authorization\", format!(\"token {}\", self.github_token))\n",
    "            .header(\"accept\", \"application/json\")\n",
    "            .header(\"editor-version\", EDITOR_VERSION)\n",
    "            .header(\"editor-plugin-version\", EDITOR_PLUGIN_VERSION)\n",
    "            .header(\"user-agent\", EDITOR_PLUGIN_VERSION)\n",
    "            .send()\n",
    "            .await?;\n",
    "\n",
    "        let status = response.status();\n",
    "        let response_text = response.text().await?;\n",
    "\n",
    "        if !status.is_success() {\n",
    "            return Err(LLMError::AuthError(format!(\n",
    "                \"Copilot usage request failed with status {status}: {response_text}\"\n",
    "            )));\n",
    "        }\n",
    "\n",
    "        let response_json: serde_json::Value = serde_json::from_str(&response_text)\n",
    "            .map_err(|e| LLMError::JsonError(e.to_string()))?;\n",
    "\n",
    "        let reset_date = response_json[\"limited_user_reset_date\"]\n",
    "            .as_i64()\n",
    "            .unwrap_or_default();\n",
    "        let reset_date_local: DateTime<Local> = DateTime::from(\n",
    "            Utc.timestamp_opt(reset_date, 0)\n",
    "                .single()\n",
    "                .unwrap_or_default()\n",
    "        );\n",
    "\n",
    "        let now = Local::now();\n",
    "        let duration = reset_date_local.signed_duration_since(now);\n",
    "        let hours_remaining = duration.num_hours();\n",
    "\n",
    "        let chat_left = response_json[\"limited_user_quotas\"][\"chat\"]\n",
    "            .as_i64()\n",
    "            .unwrap_or_default();\n",
    "        let completions_left = response_json[\"limited_user_quotas\"][\"completions\"]\n",
    "            .as_i64()\n",
    "            .unwrap_or_default();\n",
    "\n",
    "        Ok(crate::CopilotUsageInfo {\n",
    "            chat_messages_left_per_month: chat_left,\n",
    "            completions_left_per_month: completions_left,\n",
    "            reset_date: reset_date_local.format(\"%Y-%m-%d %H:%M:%S %Z\").to_string(),\n",
    "            time_remaining: format!(\"{} days {} hours\", hours_remaining / 24, hours_remaining % 24),\n",
    "        })\n",
    "    }\n"
]

# Insert the new lines
lines[insert_index:insert_index] = new_lines

# Write the modified file
with open('llm/src/backends/copilot.rs', 'w') as f:
    f.writelines(lines)

print("Successfully added fetch_copilot_usage method")
