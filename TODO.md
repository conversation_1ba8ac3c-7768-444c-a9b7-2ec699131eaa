# add a github copilot usage report on how many requests are left

check how is done in the crate 

    fn relogin_github_copilot(&self) -> Result<(), crate::error::LLMError> {


this is a bit tricky because it has to go through `llm` crate, it is here in this directory
it should report an enum value back stating that token is expired or that

on success, it returns a struct with:

chatMessagesLeftPerMonth: 50
"reset_date": "2025-07-19 02:00:00 +02:00",
"time_remaining": "10 days 4 hours" (this is relative from now to reset_date)


this works in my other codebase, adapt it into llm crate


pub async fn get_copilot_token(
client: &Client,
github_token: &str,
args: &crate::Args,
) -> Result<CopilotToken, Box<dyn StdError>> {
let response = client
.get("https://api.github.com/copilot_internal/v2/token")
.header("authorization", format!("token {}", github_token))
.header("accept", "application/json")
.header("editor-version", EDITOR_VERSION)
.header("editor-plugin-version", EDITOR_PLUGIN_VERSION)
.header("user-agent", EDITOR_PLUGIN_VERSION)
.send()
.await?;

    let status = response.status();
    let response_text = response.text().await?;

    if args.github_remaining_usage {
        let response_json: serde_json::Value = serde_json::from_str(&response_text)?;
        let reset_date = response_json["limited_user_reset_date"]
            .as_i64()
            .unwrap_or_default();
        let reset_date_local: DateTime<Local> = DateTime::from(
            Utc.timestamp_opt(reset_date, 0)
                .single()
                .unwrap_or_default()
        );

        let now = Local::now();
        let duration = reset_date_local.signed_duration_since(now);
        let hours_remaining = duration.num_hours();

        let usage_info = serde_json::json!({
            "quotas": {
                "chat": response_json["limited_user_quotas"]["chat"],
                "completions": response_json["limited_user_quotas"]["completions"]
            },
            "reset_date": reset_date_local.format("%Y-%m-%d %H:%M:%S %Z").to_string(),
            "time_remaining": format!("{} days {} hours", hours_remaining / 24, hours_remaining % 24)
        });
        println!("{}", serde_json::to_string_pretty(&usage_info)?);
    }

    if status.is_success() {
        debug_println!(args, "Copilot token request succeeded with status: {}. Response: {}", status, response_text);
    } else {
        debug_println!(args, "Copilot token request failed with status: {}", status);
        let error_details = serde_json::from_str::<ErrorResponse>(&response_text)
            .map(|e| format!(
                "message: {}, error: {}",
                e.message.unwrap_or_default(),
                e.error.unwrap_or_default()
            ))
            .unwrap_or(response_text.clone());

        return Err(format!(
            "Copilot token request failed with status {}: {}",
            status,
            error_details
        ).into());
    }

    serde_json::from_str::<CopilotToken>(&response_text)
        .map_err(|e| format!(
            "Failed to parse Copilot token response: {}. Response body: {}",
            e,
            response_text
        ).into())
}
