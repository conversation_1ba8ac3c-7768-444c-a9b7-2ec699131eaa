use crate::app::{App, <PERSON><PERSON><PERSON><PERSON><PERSON>, SystemPrompt};
use crate::cli::cli_args::Cli;
use crate::config::user_agent_config::load_dima_agent_config;
use crate::display::print::print_error;
use crate::session_management::global_state::{self, load_global_state, GlobalAppState};
use crate::session_management::session_manager::{self, Session};
use crate::utils::git::find_git_root_or_cwd;
use crate::utils::system_prompt::create_system_prompt;
use crossterm::{execute, terminal::SetTitle};
use std::error::Error;
use std::sync::{Arc, Mutex};

/// Encapsulates information about a restored session.
pub struct RestoreInfo {
    pub session_id: String,
    pub message_count: usize,
}

/// Encapsulates the application setup logic.
/// This function parses CLI arguments, loads configuration, sets up the LLM,
/// and restores a session if requested.
pub async fn setup_app(
    cli_args: Cli,
) -> Result<(Arc<Mutex<App>>, SystemPrompt, Option<RestoreInfo>), Box<dyn Error>> {
    let cwd = find_git_root_or_cwd();

    // Determine the current model, preferring global state over CLI default
    let mut current_model = cli_args.llm_provider;
    if let Ok(Some(global_state)) = load_global_state() {
        current_model = global_state.current_llm_provider;
    } else {
        let new_state = GlobalAppState::new(current_model);
        global_state::save_global_state(&new_state)?;
    }

    // Create system prompt and LLM provider
    let system_prompt = create_system_prompt(cwd.clone(), &cli_args);
    let llm = crate::provider_models::llm_provider_builder::build_llm_provider(
        system_prompt.clone().content,
        current_model.get_config(),
        &cli_args,
        0, // Initial key index is 0
    );

    // Handle session restoring logic
    let mut session_to_load: Option<Session> = None;
    let mut restore_info: Option<RestoreInfo> = None;

    let session_id = if let Some(restore_arg) = &cli_args.restore {
        if restore_arg.is_empty() {
            match session_manager::find_latest_session_for_working_dir(&cwd) {
                Ok(Some(latest_id)) => match session_manager::load_session(&latest_id) {
                    Ok(session) => {
                        let message_count = session.messages.len();
                        restore_info = Some(RestoreInfo {
                            session_id: latest_id.clone(),
                            message_count,
                        });
                        session_to_load = Some(session);
                        latest_id
                    }
                    Err(e) => {
                        print_error(format!("Failed to load session {latest_id}: {e}"));
                        session_manager::generate_session_id(&cwd)
                    }
                },
                Ok(None) => {
                    crate::display::print::print_command(
                        "No previous session found for this directory. Starting a new session.",
                    );
                    session_manager::generate_session_id(&cwd)
                }
                Err(e) => {
                    print_error(format!("Error finding latest session: {e}"));
                    session_manager::generate_session_id(&cwd)
                }
            }
        } else {
            crate::display::print::print_command(format!("Restoring session: {restore_arg}"));
            match session_manager::load_session(restore_arg) {
                Ok(session) => {
                    let message_count = session.messages.len();
                    restore_info = Some(RestoreInfo {
                        session_id: restore_arg.clone(),
                        message_count,
                    });
                    session_to_load = Some(session);
                    restore_arg.clone()
                }
                Err(e) => {
                    print_error(format!("Failed to load session {restore_arg}: {e}"));
                    session_manager::generate_session_id(&cwd)
                }
            }
        }
    } else {
        session_manager::generate_session_id(&cwd)
    };

    let dima_config = load_dima_agent_config(&cwd);

    // Build the App instance using the builder
    let mut app = AppBuilder::new()
        .working_dir(cwd.clone())
        .cli_args(cli_args)
        .system_prompt(system_prompt.clone())
        .llm(llm)
        .current_model(current_model)
        .session_id(session_id)
        .dima_config(dima_config)
        .build()?;

    // If a session was loaded, apply its state to the App instance
    if let Some(loaded_session) = session_to_load {
        app.set_conversation_messages(loaded_session.messages);
        app.set_last_response_usage_tokens(loaded_session.metadata.last_response_usage_tokens);
        app.session_allowed_bash_commands = loaded_session.metadata.allowed_bash_commands;
        app.session_denied_bash_commands = loaded_session.metadata.denied_bash_commands;
    }

    // Set terminal title
    let working_dir_name = cwd
        .file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("unknown");
    let title = format!("Dima AI - {working_dir_name}");
    if let Err(e) = execute!(std::io::stdout(), SetTitle(title)) {
        eprintln!("Error setting terminal title: {e}");
    }

    Ok((Arc::new(Mutex::new(app)), system_prompt, restore_info))
}
