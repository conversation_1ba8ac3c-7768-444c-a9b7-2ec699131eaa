use crate::cli::cli_args::MarkdownRenderEngine;
use crate::display::colors::{ORANGE_COLOR, TOOL_OUTPUT_TRUNCATE_COLOR};
use crate::display::termimad::print_by_termimad;
use console::truncate_str;
use crossterm::execute;
use crossterm::style::{Color, Print, ResetColor, SetForegroundColor, Stylize};
use crossterm::terminal::size;
use std::fmt::Display;
use std::io::{self, stdout, Write};
use std::process::{Command, Stdio};

fn print_colored(text: impl Into<String>, color: Color) {
    println!("{}", text.into().with(color));
}

fn print_by_mdcat(text: &str) -> io::Result<()> {
    let mut child = Command::new("mdcat")
        .arg("--columns")
        .arg("80")
        .arg("-") // Read from stdin
        .stdin(Stdio::piped())
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .spawn()?;

    if let Some(mut stdin) = child.stdin.take() {
        stdin.write_all(text.as_bytes())?;
    }

    let output = child.wait_with_output()?;

    if output.status.success() {
        stdout().write_all(&output.stdout)?;
    } else {
        eprintln!("`mdcat` command failed:");
        io::stderr().write_all(&output.stderr)?;
    }

    Ok(())
}

pub fn print_response(text: impl Into<String> + Display, render_engine: MarkdownRenderEngine) {
    let text_str = text.into();
    match render_engine {
        MarkdownRenderEngine::Termimad => {
            print_by_termimad(text_str.as_ref());
        }
        MarkdownRenderEngine::Mdcat => {
            if let Err(e) = print_by_mdcat(text_str.as_ref()) {
                eprintln!("Error rendering with mdcat: {e}");
                print_by_termimad(text_str.as_ref()); // Fallback to termimad
            }
            println!();
        }
    }
}

pub fn print_warning_without_newlines(text: impl Into<String> + Display) {
    print_colored(format!("{text}"), ORANGE_COLOR);
}

pub fn print_warning(text: impl Into<String> + Display) {
    print_colored(format!("\n{text}\n"), ORANGE_COLOR);
}

pub fn print_command(text: impl Into<String> + Display) {
    print_colored(format!("{text}\n"), Color::Blue);
}

pub fn print_tool_request(text: impl Into<String> + Display) {
    print_colored(format!("{text}"), Color::DarkGreen);
}

pub fn print_and_truncate_tool_output(output: &str) {
    let (terminal_width, _) = size().unwrap_or((80, 24));
    let lines: Vec<&str> = output.trim().lines().collect();
    let mut displayed_lines: Vec<String> = Vec::new();
    if lines.len() <= 15 {
        displayed_lines.extend(lines.iter().map(|&line| line.to_string()));
    } else {
        displayed_lines.extend(lines[..7].iter().map(|&line| line.to_string()));
        let lines_truncated = lines.len() - 14;
        let truncation_msg = format!("... ({lines_truncated} lines truncated) ...");
        displayed_lines.push(truncation_msg);
        displayed_lines.extend(
            lines[lines.len() - 7..]
                .iter()
                .map(|&line| line.to_string()),
        );
    }
    let full_width = terminal_width as usize;
    let mut stdout = stdout();
    let max_content_width = full_width.saturating_sub(6);
    for (index, line) in displayed_lines.iter().enumerate() {
        let truncated_line = truncate_str(line, max_content_width, "…");
        let is_truncation_msg = lines.len() > 15 && index == 7;
        execute!(stdout, Print("  "),).unwrap();
        if is_truncation_msg {
            execute!(stdout, SetForegroundColor(TOOL_OUTPUT_TRUNCATE_COLOR)).unwrap();
        }
        execute!(stdout, Print(&truncated_line), Print("\n")).unwrap();
        if is_truncation_msg {
            execute!(stdout, ResetColor).unwrap();
        }
    }
    execute!(stdout, Print("\n")).unwrap();
}

pub fn print_error(text: String) {
    print_colored(text, Color::DarkRed);
}

/// Print without any color.
pub fn print(text: impl Into<String> + Display) {
    println!("{text}");
}
