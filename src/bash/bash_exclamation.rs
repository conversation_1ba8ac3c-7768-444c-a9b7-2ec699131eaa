use crate::app::App;
use crate::config::constants::BASH_COMMANDS_TIMEOUT_SECONDS;
use llm::chat::ChatMessage;
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::process::Command as TokioCommand;
use tokio::time::timeout;

pub async fn handle_bash_command(app_arc: &Arc<Mutex<App>>, command: &str) {
    let cwd = app_arc.lock().unwrap().working_dir.clone();

    let mut bash_cmd = TokioCommand::new("bash");
    bash_cmd.args(["-c", command]);
    bash_cmd.current_dir(cwd);

    let spinner_text = format!("Executing: {command}...");
    let spinner = crate::utils::spinner::Spinner::new(&spinner_text);

    let result_str = match timeout(
        Duration::from_secs(BASH_COMMANDS_TIMEOUT_SECONDS),
        bash_cmd.output(),
    )
    .await
    {
        Ok(Ok(output)) => {
            let stdout = String::from_utf8_lossy(&output.stdout);
            let stderr = String::from_utf8_lossy(&output.stderr);
            if output.status.success() {
                if stdout.is_empty() && stderr.is_empty() {
                    Ok("Command executed successfully with no output.".to_string())
                } else {
                    Ok(format!("{stdout}{stderr}"))
                }
            } else {
                let exit_code = output.status.code().map_or(-1, |c| c);
                Err(format!(
                    "Command '{command}' failed with exit code {exit_code}\n{stdout}{stderr}"
                ))
            }
        }
        Ok(Err(e)) => Err(format!("Failed to execute command '{command}': {e}")),
        Err(_) => Err(format!(
            "Command '{command}' timed out after {BASH_COMMANDS_TIMEOUT_SECONDS} seconds"
        )),
    };
    spinner.stop().await;

    let trimmed_output = result_str.unwrap_or_else(|e| e);
    let trimmed_output = trimmed_output.trim();

    let conversation_output = format!(
        "```bash
{trimmed_output}
```"
    );

    crate::display::print::print(&conversation_output);

    let mut app = app_arc.lock().unwrap();
    app.add_message(ChatMessage::user().content(format!("!{command}")).build());
    app.add_message(
        ChatMessage::assistant()
            .content(conversation_output)
            .build(),
    );
    app.mark_code_as_changed();
}
