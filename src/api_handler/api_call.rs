use super::error_handling::handle_api_error;
use super::ApiCallResult;
use crate::app::App;
use crate::config::constants::MAX_API_RETRY_FAILURE;
use crate::display::print::print_response;
use crate::tools::utils::tool_calling::process_tool_call;
use crate::tools::utils::USER_CANCELLED_TOOL_EXECUTION;
use crate::utils::spinner::Spinner;
use llm::chat::ChatMessage;
use llm::{FunctionCall, ToolCall};
use std::error::Error;
use std::sync::{Arc, Mutex};

pub async fn do_api_call(
    app_arc: &Arc<Mutex<App>>,
    retry_count: u32,
) -> Result<ApiCallResult, Box<dyn Error>> {
    let (model_name, llm, conversation_messages, markdown_render_engine) = {
        let app = app_arc.lock().unwrap();
        let model_name = app.current_model().get_config().startup_banner_text;
        let llm = app.llm.clone();
        let conversation_messages = app.conversation_messages().clone();
        let markdown_render_engine = app.cli_args.markdown_render_engine;
        (
            model_name,
            llm,
            conversation_messages,
            markdown_render_engine,
        )
    };

    let tools = llm.tools();

    let spinner_text = if retry_count == 0 {
        format!("{model_name} is responding. Ctrl+C to abort...")
    } else {
        format!(
            "{model_name} is responding. Ctrl+C to abort... (attempt {}/{MAX_API_RETRY_FAILURE})",
            retry_count + 1
        )
    };
    let spinner = Spinner::new(spinner_text.as_str());

    let response = llm.chat_with_tools(&conversation_messages, tools).await;
    spinner.stop().await;
    match response {
        Ok(success) => {
            let mut assistant_message_builder = ChatMessage::assistant();
            let text = success.text();
            let mut has_printed_text = false;
            if let Some(text) = text {
                let trimmed_text = text.trim();
                if !trimmed_text.is_empty() {
                    assistant_message_builder = assistant_message_builder.content(&text);
                    print_response(trimmed_text, markdown_render_engine);
                    has_printed_text = true;
                }
            }

            if let Some(usage) = success.usage() {
                app_arc
                    .lock()
                    .unwrap()
                    .set_last_response_usage_tokens(Some(usage.total_tokens as usize));
            }

            let is_diff_mode = app_arc.lock().unwrap().cli_args.aider;

            if is_diff_mode {
                // In diff mode, we just care about the text content.
                // But first, we need to process potential diff blocks.
                crate::aider::diff_tool::process_diff_edit(
                    app_arc,
                    &success.text().unwrap_or_default(),
                );
                let text = success.text().unwrap_or_default();
                let assistant_message = ChatMessage::assistant().content(text).build();
                app_arc.lock().unwrap().add_message(assistant_message);
                Ok(ApiCallResult::ContinueWithUserInteraction)
            } else {
                let mut result = ApiCallResult::ContinueWithUserInteraction;
                let mut assistant_message = assistant_message_builder.build();

                if let Some(tool_calls) = success.tool_calls() {
                    if !tool_calls.is_empty() {
                        if has_printed_text {
                            println!();
                        }
                        result = ApiCallResult::ContinueWithoutUserInteraction;
                        assistant_message = ChatMessage::assistant()
                            .tool_use(tool_calls.clone())
                            .build();

                        let mut new_tool_messages = vec![];
                        let mut tool_execution_failed = false; // Flag to track tool execution success

                        for call in &tool_calls {
                            let tool_name = &call.function.name;
                            match process_tool_call(app_arc.clone(), call).await {
                                Ok(tool_output_content) => {
                                    let tool_output_json =
                                        serde_json::to_string(&tool_output_content)?;
                                    new_tool_messages.push(
                                        ChatMessage::user()
                                            .tool_result(vec![ToolCall {
                                                id: call.id.clone(),
                                                call_type: "function".to_string(),
                                                function: FunctionCall {
                                                    name: tool_name.into(),
                                                    arguments: tool_output_json,
                                                },
                                            }])
                                            .content("")
                                            .build(),
                                    );
                                }
                                Err(e) => {
                                    if e == USER_CANCELLED_TOOL_EXECUTION {
                                        return Ok(ApiCallResult::ContinueWithUserInteraction);
                                    }

                                    tool_execution_failed = true; // Set flag on failure
                                    let error_message =
                                        format!("Error executing tool {tool_name}: {e}");
                                    let tool_output_json = serde_json::to_string(&error_message)?;
                                    new_tool_messages.push(
                                        ChatMessage::user()
                                            .tool_result(vec![ToolCall {
                                                id: call.id.clone(),
                                                call_type: "function".to_string(),
                                                function: FunctionCall {
                                                    name: tool_name.into(),
                                                    arguments: tool_output_json,
                                                },
                                            }])
                                            .content("")
                                            .build(),
                                    );
                                }
                            }
                        }

                        // Atomically add all new messages at once.
                        // This prevents leaving the conversation in a broken state if the user aborts
                        // during tool execution.
                        let mut app = app_arc.lock().unwrap();
                        let mut current_messages = app.conversation_messages().clone();
                        current_messages.push(assistant_message); // Add the assistant's tool_use message
                        current_messages.extend(new_tool_messages); // Add the tool_result messages
                        app.set_conversation_messages(current_messages);

                        return if tool_execution_failed {
                            Ok(ApiCallResult::ToolFailed)
                        } else {
                            Ok(result) // Return early as messages are already set
                        };
                    }
                }
                // If no tool calls, or tool calls were empty, add the regular assistant message
                app_arc.lock().unwrap().add_message(assistant_message);
                Ok(result)
            }
        }
        Err(e) => handle_api_error(app_arc, e).await,
    }
}
