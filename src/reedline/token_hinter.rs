use crate::app::App;
use crate::tokens::token_estimator::estimate_tokens;
use nu_ansi_term::{Color, Style};
use reedline::{Hinter, History, SearchQuery};
use std::sync::{Arc, Mutex};

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct CurrentPromptBufferState {
    pub current_buffer_content: String,
}

/// A hinter that combines history-based hints with real-time token counting
/// and updates the prompt buffer state on every character input
pub struct TokenCountingHinter {
    style: Style,
    overload_style: Style,
    current_hint: String,
    min_chars: usize,
    app_arc: Arc<Mutex<App>>,
    prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
}

impl TokenCountingHinter {
    pub fn new(
        app_arc: Arc<Mutex<App>>,
        prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
    ) -> Self {
        Self {
            style: Style::new().fg(Color::LightGray),
            overload_style: Style::new().fg(Color::Red),
            current_hint: String::new(),
            min_chars: 1,
            app_arc,
            prompt_buffer_state,
        }
    }

    /// A builder that sets the style applied to the hint as part of the buffer
    #[must_use]
    pub fn with_style(mut self, style: Style) -> Self {
        self.style = style;
        self
    }

    /// Update the prompt buffer state with the current line content
    fn update_buffer_content(&self, content: &str) {
        if let Ok(mut buffer_state) = self.prompt_buffer_state.lock() {
            buffer_state.current_buffer_content = content.to_string();
        }
    }

    /// Calculate the total token count for the current buffer content with caching and throttling
    /// Returns (real_tokens, estimated_tokens).
    fn get_token_count_for_buffer(&self, buffer_content: &str) -> (usize, usize) {
        let real_tokens = if let Ok(app) = self.app_arc.lock() {
            app.last_response_usage_tokens().unwrap_or(0)
        } else {
            0
        };
        let mut estimated_tokens = estimate_tokens(buffer_content);
        // Add system prompt tokens if buffer content is empty or only whitespace
        if let Ok(app) = self.app_arc.lock() {
            if app.conversation_messages().is_empty() {
                estimated_tokens += estimate_tokens(&app.system_prompt.content);
            }
        }
        (real_tokens, estimated_tokens)
    }

    /// Get the first token from a string (similar to get_first_token from hinter module)
    fn get_first_token(input: &str) -> String {
        input
            .split_whitespace()
            .next()
            .unwrap_or_default()
            .to_string()
    }
}

impl Hinter for TokenCountingHinter {
    fn handle(
        &mut self,
        line: &str,
        _pos: usize,
        history: &dyn History,
        _use_ansi_coloring: bool,
        _current_dir: &str,
    ) -> String {
        // Update the prompt buffer state with the current line content
        // This is the key functionality that makes token counting work in real-time
        self.update_buffer_content(line);

        // Generate history-based hint (similar to DefaultHinter)
        self.current_hint = if line.chars().count() >= self.min_chars {
            // Try to find a history entry that starts with the current line
            if let Ok(search_results) = history.search(SearchQuery::last_with_prefix(
                line.to_string(),
                history.session(),
            )) {
                search_results.first().map_or_else(String::new, |entry| {
                    entry
                        .command_line
                        .get(line.len()..)
                        .unwrap_or_default()
                        .to_string()
                })
            } else {
                String::new()
            }
        } else {
            String::new()
        };
        let (real_tokens, estimated_tokens) = self.get_token_count_for_buffer(line);
        let total_tokens = real_tokens + estimated_tokens;

        let context_window_size = if let Ok(app) = self.app_arc.lock() {
            app.current_model().get_config().context_window_size
        } else {
            1_000_000 // Default value if app lock fails
        };

        let token_display = if total_tokens == 0 {
            self.style.paint("   ").to_string()
                + &self.style.paint("[100% context left]").to_string()
        } else {
            let percentage_used = (total_tokens * 100) / context_window_size;
            if percentage_used >= 100 {
                self.style.paint("   ").to_string()
                    + &self.overload_style.paint("[Context Overload]").to_string()
            } else {
                let percentage_left = 100 - percentage_used;
                self.style.paint("   ").to_string()
                    + &self
                        .style
                        .paint(format!("[{percentage_left}% context left]"))
                        .to_string()
            }
        };
        self.style
            .paint(self.current_hint.clone() + &token_display)
            .to_string()
    }

    fn complete_hint(&self) -> String {
        self.current_hint.clone()
    }

    fn next_hint_token(&self) -> String {
        Self::get_first_token(&self.current_hint)
    }
}
