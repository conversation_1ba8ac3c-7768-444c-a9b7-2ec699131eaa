use crate::app::App;
use crate::reedline::dot_command_completer::DotCommandCompleter;
use crate::reedline::token_hinter::{CurrentPromptBufferState, TokenCountingHinter};
use crate::session_management::session_dir::get_prompt_history_path;
use nu_ansi_term::{Color, Style};
use reedline::{
    ColumnarMenu, CursorConfig, EditCommand, FileBackedHistory, KeyCode, KeyModifiers, Keybindings,
    ListMenu, MenuBuilder, Prompt, PromptEditMode, PromptHistorySearch, PromptHistorySearchStatus,
    Reedline, ReedlineError, ReedlineEvent, ReedlineMenu, Signal,
};
use std::borrow::Cow;
use std::io;
use std::path::PathBuf;
use std::process::Command;
use std::sync::{Arc, Mutex};
use thiserror::Error;

const QUIT_MARKER: &str = ":::SUSPENDABLE_REEDLINE_QUIT:::";
const RETRY_MARKER: &str = ":::SUSPENDABLE_REEDLINE_RETRY:::";
const CHANGE_MODEL_MARKER: &str = ":::SUSPENDABLE_REEDLINE_MODEL:::";

/// Transient prompt that replaces previous prompts with a simple indicator
pub struct MyPrompt;

impl Prompt for MyPrompt {
    fn render_prompt_left(&self) -> Cow<str> {
        Cow::Owned(String::new())
    }

    fn render_prompt_right(&self) -> Cow<str> {
        Cow::Owned(String::new())
    }

    fn render_prompt_indicator(&self, _prompt_mode: PromptEditMode) -> Cow<str> {
        Cow::Borrowed("> ")
    }

    fn render_prompt_multiline_indicator(&self) -> Cow<str> {
        Cow::Borrowed("> ")
    }

    fn render_prompt_history_search_indicator(
        &self,
        history_search: PromptHistorySearch,
    ) -> Cow<str> {
        let prefix = match history_search.status {
            PromptHistorySearchStatus::Passing => "",
            PromptHistorySearchStatus::Failing => "failing ",
        };

        Cow::Owned(format!(
            "({}reverse-search: {}) ",
            prefix, history_search.term
        ))
    }
}

#[derive(Error, Debug)]
pub enum SuspendableError {
    #[error("Reedline error: {0}")]
    Reedline(#[from] ReedlineError),

    #[error("Failed during suspend operation: {0}")]
    Suspend(#[from] io::Error),
}

pub type SuspendableResult<T> = Result<T, SuspendableError>;

pub struct SuspendableReedline {
    pub editor: Reedline,
}

#[derive(Debug)]
pub enum ReadResult {
    DoNothing,
    Success(String),
    ShouldQuit,
    Retry,
    ChangeModel,
    ShowDotCommands,
}

impl SuspendableReedline {
    pub fn create_with_history_and_hinter(
        app_arc: Arc<Mutex<App>>,
        prompt_buffer_state_arc: Arc<Mutex<CurrentPromptBufferState>>,
    ) -> Self {
        let hinter = TokenCountingHinter::new(app_arc.clone(), prompt_buffer_state_arc.clone())
            .with_style(Style::new().fg(Color::DarkGray));

        let mut bindings = Keybindings::default();
        let ctrl = KeyModifiers::CONTROL;
        let quit_event = ReedlineEvent::ExecuteHostCommand(QUIT_MARKER.to_string());
        let retry_event = ReedlineEvent::ExecuteHostCommand(RETRY_MARKER.to_string());
        let change_model_event = ReedlineEvent::ExecuteHostCommand(CHANGE_MODEL_MARKER.to_string());
        bindings.add_binding(
            ctrl,
            KeyCode::Char('u'),
            ReedlineEvent::Edit(vec![EditCommand::Clear]),
        );
        bindings.add_binding(ctrl, KeyCode::Char('r'), retry_event.clone());
        bindings.add_binding(ctrl, KeyCode::Char('R'), retry_event);
        bindings.add_binding(ctrl, KeyCode::Char('d'), quit_event.clone());
        bindings.add_binding(ctrl, KeyCode::Char('D'), quit_event);
        bindings.add_binding(ctrl, KeyCode::Char('h'), change_model_event.clone());
        bindings.add_binding(ctrl, KeyCode::Char('H'), change_model_event);
        bindings.add_binding(
            KeyModifiers::NONE,
            KeyCode::Left,
            ReedlineEvent::UntilFound(vec![ReedlineEvent::MenuLeft, ReedlineEvent::Left]),
        );
        bindings.add_binding(
            KeyModifiers::NONE,
            KeyCode::Right,
            ReedlineEvent::UntilFound(vec![
                ReedlineEvent::HistoryHintComplete,
                ReedlineEvent::MenuRight,
                ReedlineEvent::Right,
            ]),
        );
        bindings.add_binding(KeyModifiers::NONE, KeyCode::Enter, ReedlineEvent::Enter);
        bindings.add_binding(
            KeyModifiers::NONE,
            KeyCode::Backspace,
            ReedlineEvent::Edit(vec![EditCommand::Backspace]),
        );

        Self::add_menu_keybindings(&mut bindings);

        let history = match FileBackedHistory::with_file(500, get_prompt_history_path()) {
            Ok(h) => Some(Box::new(h) as Box<dyn reedline::History>),
            Err(e) => {
                eprintln!("Error creating history file: {e}");
                None
            }
        };

        // Create cursor config
        let cursor_config = CursorConfig {
            vi_insert: Some(crossterm::cursor::SetCursorStyle::BlinkingBar),
            vi_normal: Some(crossterm::cursor::SetCursorStyle::SteadyBlock),
            emacs: None,
        };

        let command_completer = Box::new(DotCommandCompleter::new());

        // Create and configure the editor
        let mut editor = Reedline::create()
            .with_edit_mode(Box::new(reedline::Emacs::new(bindings)))
            .with_cursor_config(cursor_config)
            .with_hinter(Box::new(hinter))
            .with_completer(command_completer)
            .use_bracketed_paste(true)
            .with_ansi_colors(true)
            .with_transient_prompt(Box::new(MyPrompt {}));

        if let Some(h) = history {
            editor = editor.with_history(h);
            editor = editor.with_history_exclusion_prefix(Some(" ".to_string()));
        }

        editor = editor
            .with_menu(ReedlineMenu::EngineCompleter(Box::new(
                ColumnarMenu::default()
                    .with_name("completion_menu")
                    .with_marker("│ "),
            )))
            .with_menu(ReedlineMenu::HistoryMenu(Box::new(
                ListMenu::default().with_name("history_menu"),
            )));

        SuspendableReedline { editor }
    }

    /// Reads a line from the user. Ctrl+C is ignored, Ctrl+D is used for instant quit.
    pub fn read_line(&mut self) -> SuspendableResult<ReadResult> {
        match self.editor.read_line(&MyPrompt {}) {
            Ok(Signal::Success(buffer)) => {
                if buffer == QUIT_MARKER {
                    Ok(ReadResult::ShouldQuit)
                } else if buffer == RETRY_MARKER {
                    Ok(ReadResult::Retry)
                } else if buffer == CHANGE_MODEL_MARKER {
                    Ok(ReadResult::ChangeModel)
                } else if buffer == "." {
                    Ok(ReadResult::ShowDotCommands)
                } else {
                    Ok(ReadResult::Success(buffer))
                }
            }
            Ok(Signal::CtrlD) => Ok(ReadResult::ShouldQuit),
            Ok(Signal::CtrlC) => Ok(ReadResult::DoNothing),
            Err(e) => Err(e.into()),
        }
    }

    fn add_menu_keybindings(keybindings: &mut Keybindings) {
        keybindings.add_binding(
            KeyModifiers::NONE,
            KeyCode::Tab,
            ReedlineEvent::UntilFound(vec![
                ReedlineEvent::Menu("completion_menu".to_string()),
                ReedlineEvent::MenuNext,
                ReedlineEvent::Edit(vec![EditCommand::Complete]),
            ]),
        );
        keybindings.add_binding(
            KeyModifiers::SHIFT,
            KeyCode::BackTab,
            ReedlineEvent::MenuPrevious,
        );

        // Add arrow key navigation for the completion menu
        keybindings.add_binding(
            KeyModifiers::NONE,
            KeyCode::Up,
            ReedlineEvent::UntilFound(vec![ReedlineEvent::MenuPrevious, ReedlineEvent::Up]),
        );
        keybindings.add_binding(
            KeyModifiers::NONE,
            KeyCode::Down,
            ReedlineEvent::UntilFound(vec![ReedlineEvent::MenuNext, ReedlineEvent::Down]),
        );
    }

    pub fn with_buffer_editor(mut self, editor_command: Command, temp_file: PathBuf) -> Self {
        self.editor = self.editor.with_buffer_editor(editor_command, temp_file);
        self
    }
}
