use crate::provider_models::llm_provider_config::LlmProviderType;
use clap::{<PERSON><PERSON><PERSON>, ValueEnum};

#[derive(ValueEnum, Debug, Clone, Copy, PartialEq, Eq)]
pub enum EditTool {
    OnlyWriteFile,
    WriteFileAndFindAndReplace,
    OnlyFindAndReplace,
}

#[derive(ValueEnum, Debug, Clone, Copy, PartialEq, Eq)]
pub enum MarkdownRenderEngine {
    Termimad,
    Mdcat,
}

#[derive(<PERSON><PERSON><PERSON>, Debug, Clone)]
#[command(author, version, about, long_about = None)]
pub struct Cli {
    #[arg(
        long,
        value_name = "PORT",
        help = "The localhost proxy port to inspect network requests"
    )]
    pub proxy_localhost: Option<u16>,

    #[arg(long, value_name = "SESSION_ID", num_args = 0..=1, default_missing_value = "", help = "Restore a previous session")]
    pub restore: Option<String>,

    #[arg(
        long,
        value_name = "TOKEN_LIMIT",
        help = "Token limit for workspace injection",
        default_value = "1200"
    )]
    pub workspace_token_limit: usize,

    #[arg(
        long,
        value_name = "MESSAGE",
        help = "Initial message to send to API before starting interactive session"
    )]
    pub initial_message: Option<String>,

    #[arg(
        long,
        help = "Print the current workspace structure to stdout and exit"
    )]
    pub debug_workspace: bool,

    #[arg(
        long,
        value_name = "NOTIFICATION_TYPE",
        help = "Notification type to use when user input is required to continue"
    )]
    pub notification_type: Option<NotificationType>,

    #[arg(
        long,
        help = "Execute the .yek command initially to paste in all files in the working directory"
    )]
    pub yek: bool,

    #[arg(
        long,
        value_name = "LLM_PROVIDER",
        help = "LLM provider to use",
        default_value = "PollinationsAiGpt4_1"
    )]
    pub llm_provider: LlmProviderType,

    #[arg(
        long,
        help = "Use diff format for edits, similar to aider, and do not send tools to the LLM"
    )]
    pub aider: bool,

    #[arg(long, help = "Skip bash permissions entirely")]
    pub yolo: bool,

    #[arg(
        long,
        value_enum,
        help = "Specifies which file editing tools to enable",
        default_value_t = EditTool::OnlyWriteFile,
    )]
    pub edit_tool: EditTool,

    #[arg(long, help = "Do not append any tools to the LLM")]
    pub no_tools: bool,

    #[arg(
        long,
        value_enum,
        help = "Specifies which markdown rendering engine to use",
        default_value_t = MarkdownRenderEngine::Mdcat,
    )]
    pub markdown_render_engine: MarkdownRenderEngine,
}

#[derive(ValueEnum, Debug, Clone, Copy, PartialEq, Eq)]
pub enum NotificationType {
    RaycastConfetti,
    TerminalNotifier,
    Hammerspoon,
}
