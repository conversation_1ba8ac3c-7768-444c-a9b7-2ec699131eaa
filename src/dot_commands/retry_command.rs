use crate::app::App;
use crate::display::print::{print_error, print_warning};
use crate::dot_commands::handler::command_handler::DotCommandResult;
use llm::chat::ChatRole;
use std::sync::{Arc, Mutex};

pub fn handle_retry_command(app_arc: &Arc<Mutex<App>>) -> DotCommandResult {
    let app = app_arc.lock().unwrap();
    if let Some(last_message) = app.conversation_messages().last() {
        if last_message.role == ChatRole::User {
            DotCommandResult::Retry
        } else {
            let warning_message =
                "Cannot retry: The last message was not from the user. Just type your message.";
            print_warning(warning_message);
            DotCommandResult::Continue
        }
    } else {
        print_error("No conversation history to retry".into());
        DotCommandResult::Continue
    }
}
