use crate::app::App;
use crate::display::print::{print_command, print_error};
use crate::utils::clipboard::copy_to_clipboard;
use std::sync::{Arc, Mutex};

pub fn handle_all_command(app_arc: &Arc<Mutex<App>>) {
    let all = {
        let app_locked = app_arc.lock().unwrap();
        app_locked
            .conversation_messages()
            .iter()
            .map(|msg| msg.content.clone())
            .collect::<Vec<_>>()
            .join("\n\n")
    };

    match copy_to_clipboard(&all) {
        Ok(_) => {
            print_command("All conversation history copied to clipboard");
        }
        Err(e) => {
            print_error(format!(
                "Failed to copy conversation history to clipboard: {e}"
            ));
        }
    }
}
