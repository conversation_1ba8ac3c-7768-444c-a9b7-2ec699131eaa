use crate::app::App;
use crate::display::print::{print, print_error};
use std::process::Command;
use std::sync::{Arc, Mutex};

pub fn handle_diff_command(app_arc: &Arc<Mutex<App>>) {
    let app = app_arc.lock().unwrap();
    let cwd = &app.working_dir;

    let output = Command::new("git")
        .arg("diff")
        .arg("--stat")
        .arg("--color=always")
        .arg("HEAD")
        .current_dir(cwd)
        .output();

    match output {
        Ok(output) => {
            if output.status.success() {
                let stdout = String::from_utf8_lossy(&output.stdout);
                print(stdout.trim_end().to_string() + "\n");
            } else {
                let stderr = String::from_utf8_lossy(&output.stderr);
                print_error(format!("git diff failed: {stderr}"));
            }
        }
        Err(e) => {
            print_error(format!("Failed to execute git diff: {e}"));
        }
    }
}
