use crate::app::App;
use crate::display::print::{print_command, print_error};
use crate::utils::clipboard::copy_to_clipboard;
use llm::chat::ChatRole;
use std::sync::{Arc, Mutex};

pub fn handle_last_command(app_arc: &Arc<Mutex<App>>) {
    let last_content = {
        let app_locked = app_arc.lock().unwrap();
        app_locked
            .conversation_messages()
            .iter()
            .rev()
            .find(|msg| msg.role == ChatRole::Assistant)
            .map(|msg| msg.content.clone())
    };
    match last_content {
        Some(content) => match copy_to_clipboard(content.as_str()) {
            Ok(_) => {
                print_command("Last AI response copied to clipboard");
            }
            Err(e) => {
                print_error(format!("Failed to copy last response to clipboard: {e}"));
            }
        },
        None => {
            print_error("No AI responses found".into());
        }
    }
}
