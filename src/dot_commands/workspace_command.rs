use crate::app::App;
use crate::display::print::{print_command, print_error};
use crate::utils::clipboard::copy_to_clipboard;
use std::sync::{Arc, Mutex};

pub fn handle_workspace_command(app_arc: &Arc<Mutex<App>>) {
    let workspace_content = {
        let app_locked = app_arc.lock().unwrap();
        app_locked.system_prompt.workspace_content.clone()
    };

    match copy_to_clipboard(&workspace_content) {
        Ok(_) => {
            print_command("Workspace content copied to clipboard");
        }
        Err(e) => {
            print_error(format!(
                "Failed to copy workspace content to clipboard: {e}"
            ));
        }
    }
}
