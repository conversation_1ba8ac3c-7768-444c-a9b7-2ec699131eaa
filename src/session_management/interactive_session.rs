use crate::api_handler::{call_api, Api<PERSON>all<PERSON><PERSON>ult};
use crate::app::App;
use crate::app_setup::setup_app;
use crate::bash::bash_exclamation::handle_bash_command;
use crate::cli::cli_args::Cli;
use crate::config::constants::MAX_API_RETRY_FAILURE;
use crate::display::print::print_error;
use crate::display::startup_banner::print_startup_banner;
use crate::dot_commands::commands_command::handle_commands_command;
use crate::dot_commands::handler::command_handler::{
    parse_input_into_command, CommandContext, DotCommandResult,
};
use crate::dot_commands::model_command::open_provider_picker;
use crate::dot_commands::retry_command::handle_retry_command;
use crate::linter::automatic_linter::run_lint_command_if_needed;
use crate::notifications::show_user_input_required_notification;
use crate::reedline::suspendable_reedline::{ReadResult, SuspendableReedline};
use crate::reedline::token_hinter::CurrentPromptBufferState;
use crate::utils::git::find_git_root_or_cwd;
use crate::workspace::workspace_for_system_prompt::create_workspace_for_system_prompt;
use clap::Parser;
use llm::chat::ChatMessage;
use std::env::temp_dir;
use std::error::Error;
use std::io::IsTerminal;
use std::process::Command;
use std::sync::{Arc, Mutex};

pub async fn start() -> Result<(), Box<dyn Error>> {
    let cli_args = Cli::parse();
    let (app_arc, system_prompt, restore_info) = setup_app(cli_args.clone()).await?;
    let is_interactive = std::io::stdin().is_terminal();

    // Handle --debug-workspace as a special case that exits early
    if cli_args.debug_workspace {
        let cwd = find_git_root_or_cwd();
        return match create_workspace_for_system_prompt(cwd, cli_args.workspace_token_limit) {
            Ok(workspace) => {
                println!("{workspace}");
                Ok(())
            }
            Err(e) => Err(e),
        };
    }

    if !is_interactive {
        if cli_args.initial_message.is_none() {
            eprintln!("In non-interactive mode, --initial-message is required.");
            std::process::exit(1);
        } else {
            // If it's not interactive and an initial message is provided, process it and exit.
            let initial_message = cli_args.initial_message.clone().unwrap();
            app_arc
                .lock()
                .unwrap()
                .add_message(ChatMessage::user().content(initial_message.clone()).build());
            // We need a retry mechanism here as well.
            handle_api_call_with_retries(&app_arc.clone()).await;
            return Ok(());
        }
    }

    // Extract necessary info for banner and loop after app is created
    let (working_dir, session_id, model_type) = {
        let app = app_arc.lock().unwrap();
        (
            app.working_dir.clone(),
            app.session_id.clone(),
            app.current_model(),
        )
    };

    print_startup_banner(
        &working_dir,
        cli_args.proxy_localhost,
        model_type,
        system_prompt.with_custom_user_prompt,
        restore_info.map(|info| {
            format!(
                "Restored {} messages from session: {}",
                info.message_count, info.session_id
            )
        }),
    );

    // Create a suspendable editor with external editor support and custom hinter
    let temp_file = temp_dir().join("rust_llm_tui_edit_buffer.tmp");
    let editor_cmd_str = std::env::var("EDITOR").unwrap_or_else(|_| "emacsclient".to_string());
    let mut editor_os_cmd = Command::new(editor_cmd_str);
    editor_os_cmd.arg(&temp_file);

    let prompt_buffer_state_arc = Arc::new(Mutex::new(CurrentPromptBufferState::default()));

    /// Contains the retry loop for API calls.
    async fn handle_api_call_with_retries(app_arc: &Arc<Mutex<App>>) -> ApiCallResult {
        let max_retries = MAX_API_RETRY_FAILURE;
        for i in 0..max_retries {
            // call_api is the one that has the `select!` for ctrl+c
            let result = call_api(app_arc, i).await;

            // call_api returns ContinueWithUserInteraction on Ctrl+C during sleep
            // and Retry on successful sleep.
            if let ApiCallResult::Retry(_) = result {
                continue; // Loop to retry
            } else {
                return result; // Return on success, tool fail, or user abort
            }
        }
        print_error("Maximum retries reached. Aborting.".to_string());
        ApiCallResult::ContinueWithUserInteraction
    }

    let mut line_editor = SuspendableReedline::create_with_history_and_hinter(
        app_arc.clone(),
        prompt_buffer_state_arc.clone(),
    )
    .with_buffer_editor(editor_os_cmd, temp_file);

    let mut notification_required_on_user_input = false;
    let mut call_api_without_user_interaction = cli_args.initial_message.is_some();

    // Handle initial commands from CLI args
    if cli_args.yek {
        if let Some(command) = parse_input_into_command(".yek") {
            command.execute(CommandContext {
                input: ".yek",
                session_id: &session_id,
                app_arc: &app_arc,
            });
        } else {
            print_error("Failed to parse .yek command.".to_string());
        }
    }
    if let Some(initial_message) = &cli_args.initial_message {
        println!("> {initial_message}");
        app_arc
            .lock()
            .unwrap()
            .add_message(ChatMessage::user().content(initial_message.clone()).build());
    }

    'main_loop: loop {
        let is_aborted;
        {
            let mut app = app_arc.lock().unwrap();
            is_aborted = app.is_aborted_by_ctrl_c();
            if is_aborted {
                app.set_is_aborted_by_ctrl_c(false);
                call_api_without_user_interaction = false; // Ensure human-turn after abort
                                                           // Don't trigger notification on ctrl-c request abortions
                notification_required_on_user_input = false;
            }
        } // `app` (MutexGuard) is dropped here

        if !call_api_without_user_interaction && !is_aborted {
            let lint_result = run_lint_command_if_needed(&app_arc).await;
            if let Some(lint_output) = lint_result {
                // If linting failed, add the output to the conversation and let the AI respond
                app_arc
                    .lock()
                    .unwrap()
                    .add_message(ChatMessage::user().content(lint_output).build());
                call_api_without_user_interaction = true;
            }
        }

        // If it's a human turn and a notification is due
        if !call_api_without_user_interaction && notification_required_on_user_input {
            show_user_input_required_notification(&cli_args);
            notification_required_on_user_input = false;
        }
        if call_api_without_user_interaction {
            let api_call_result = handle_api_call_with_retries(&app_arc.clone()).await;
            // The result is now final (not a retry). We just need to know if we should
            // continue the machine-turn or switch to a human-turn.
            call_api_without_user_interaction = matches!(
                api_call_result,
                ApiCallResult::ContinueWithoutUserInteraction | ApiCallResult::ToolFailed
            );
            // If API call just finished and it's now human's turn, set flag for next iteration
            if !call_api_without_user_interaction {
                notification_required_on_user_input = true;
            }
        } else {
            let read_result = line_editor.read_line();
            println!();
            if let Ok(result) = read_result {
                match result {
                    ReadResult::DoNothing => {}
                    ReadResult::Success(success) => {
                        let trimmed_buffer = success.trim();
                        if trimmed_buffer.is_empty() {
                            // If the buffer is empty, do nothing and continue the loop
                        } else if let Some(command_to_execute) = trimmed_buffer.strip_prefix('!') {
                            let command_to_execute = command_to_execute.trim();
                            if command_to_execute.is_empty() {
                                print_error("Empty bash command.".to_string());
                            } else {
                                handle_bash_command(&app_arc, command_to_execute).await;
                            }
                        } else if trimmed_buffer.starts_with('.') {
                            match parse_input_into_command(trimmed_buffer) {
                                None => {
                                    print_error(format!(
                                        "Unknown command: {trimmed_buffer}, hit tab after typing dot to see all commands"
                                    ));
                                }
                                Some(command) => {
                                    notification_required_on_user_input = false;
                                    let command_result = command.execute(CommandContext {
                                        input: trimmed_buffer,
                                        session_id: &session_id,
                                        app_arc: &app_arc,
                                    });
                                    match command_result {
                                        DotCommandResult::Exit => break 'main_loop,
                                        DotCommandResult::Continue => {
                                            /* Do nothing, continue loop */
                                            // Don't trigger notification after executing dot commands
                                            notification_required_on_user_input = false;
                                        }
                                        DotCommandResult::Retry => {
                                            call_api_without_user_interaction = true;
                                        }
                                    }
                                }
                            }
                        } else {
                            {
                                app_arc
                                    .lock()
                                    .unwrap()
                                    .add_message(ChatMessage::user().content(success).build());
                            } // `app` (MutexGuard) is dropped here
                            let api_call_result =
                                handle_api_call_with_retries(&app_arc.clone()).await;
                            call_api_without_user_interaction = matches!(
                                api_call_result,
                                ApiCallResult::ContinueWithoutUserInteraction
                                    | ApiCallResult::ToolFailed
                            );
                            if !call_api_without_user_interaction {
                                notification_required_on_user_input = true;
                            }
                        }
                    }
                    ReadResult::ShouldQuit => {
                        break 'main_loop;
                    }
                    ReadResult::Retry => match handle_retry_command(&app_arc.clone()) {
                        DotCommandResult::Exit => break 'main_loop,
                        DotCommandResult::Continue => {
                            // Don't trigger notification after executing dot commands
                            notification_required_on_user_input = false;
                        }
                        DotCommandResult::Retry => {
                            call_api_without_user_interaction = true;
                        }
                    },
                    ReadResult::ShowDotCommands => {
                        handle_commands_command(CommandContext {
                            input: ".",
                            session_id: &session_id,
                            app_arc: &app_arc,
                        });
                        // Don't trigger notification after executing dot commands
                        notification_required_on_user_input = false;
                    }
                    ReadResult::ChangeModel => {
                        open_provider_picker(CommandContext {
                            input: ".model",
                            session_id: &session_id,
                            app_arc: &app_arc,
                        });
                        // Don't trigger notification after changing model
                        notification_required_on_user_input = false;
                    }
                }
            }
        }
    }
    Ok(())
}
