use crate::cli::cli_args::Cli;
use crate::config::constants::NETWORK_TIMEOUT_SECONDS;
use crate::display::print::print_error;
use crate::provider_models::common_config::LlmProviderConfig;
use crate::session_management::session_dir::get_session_dir;
use crate::tools::utils::llm_provider_tool_setup::setup_tools;
use llm::builder::LLMBuilder;
use llm::LLMProvider;
use std::env::var;

pub fn build_llm_provider(
    system_prompt: String,
    model_config: LlmProviderConfig,
    cli_args: &Cli,
    active_api_key_index: usize,
) -> Box<dyn LLMProvider> {
    let mut builder = LLMBuilder::new()
        .backend(model_config.backend.into())
        .model(&model_config.api_model_name)
        .system(system_prompt)
        .temperature(0.7)
        .timeout_seconds(NETWORK_TIMEOUT_SECONDS);

    if model_config.is_github_copilot {
        builder = builder.github_copilot_token_directory(get_session_dir());
    } else {
        let available_keys: Vec<String> = model_config
            .api_key_env_names
            .iter()
            .filter_map(|name| var(name).ok())
            .collect();

        if !available_keys.is_empty() {
            let key_to_use = &available_keys[active_api_key_index % available_keys.len()];
            builder = builder.api_key(key_to_use.clone());
        } else if !model_config.api_key_env_names.is_empty() {
            print_error(format!(
                "None of the environment API keys are set for this provider: {:?}",
                model_config.api_key_env_names
            ));
        }
    }

    if let Some(base_url) = model_config.base_url {
        builder = builder.base_url(&base_url);
    }
    if !cli_args.aider && !cli_args.no_tools {
        builder = setup_tools(builder, cli_args);
    }

    let proxy_url = cli_args
        .proxy_localhost
        .map(|port| format!("http://localhost:{port}"));

    if let Some(proxy_url) = proxy_url {
        builder = builder.proxy_url(proxy_url);
    }
    builder.build().expect("Failed to build LLM")
}
