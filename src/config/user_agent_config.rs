use crate::display::print::print_error;
use serde::Deserialize;
use std::fs;
use std::path::Path;

#[derive(Deserialize, Debug, <PERSON><PERSON>ult, <PERSON><PERSON>)]
#[serde(deny_unknown_fields)]
pub struct DimaAgentConfig {
    #[serde(rename = "lintCommand")]
    pub lint_command: Option<String>,
}

pub fn load_dima_agent_config(workspace_path: &Path) -> Option<DimaAgentConfig> {
    let config_path = workspace_path.join("dima-agent.json");
    if !config_path.exists() {
        return Some(DimaAgentConfig::default()); // No config file is not an error
    }

    let content = match fs::read_to_string(&config_path) {
        Ok(c) => c,
        Err(e) => {
            print_error(format!(
                "Error reading dima-agent.json: {e}. Ignoring config."
            ));
            return None;
        }
    };

    if content.trim().is_empty() {
        return Some(DimaAgentConfig::default());
    }

    match serde_json::from_str::<DimaAgentConfig>(&content) {
        Ok(config) => Some(config),
        Err(e) => {
            print_error(format!(
                "Error parsing dima-agent.json: {e}. Ignoring config."
            ));
            None
        }
    }
}
