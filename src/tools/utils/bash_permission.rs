use crate::app::App;
use crate::display::print::print_warning;
use crate::session_management::global_state::{load_global_state, save_global_state};
use dialoguer::{theme::ColorfulTheme, Select};
use std::sync::{Arc, Mutex};

#[derive(Debug, PartialEq, Eq)]
pub enum PermissionStatus {
    Allowed,
    Denied,
    Cancelled,
}

/// Checks if the bash command is allowed. If not, asks the user for permission.
/// Returns a `PermissionStatus` indicating the outcome.
pub fn check_bash_permission(app_arc: &Arc<Mutex<App>>, command: &str) -> PermissionStatus {
    let mut app = app_arc.lock().unwrap();
    let mut global_state = load_global_state().unwrap().unwrap();

    // Check if command is globally allowed
    if global_state
        .allowed_bash_commands
        .contains(&command.to_string())
    {
        return PermissionStatus::Allowed;
    }

    // Check if command is globally denied
    if global_state
        .denied_bash_commands
        .contains(&command.to_string())
    {
        return PermissionStatus::Denied;
    }

    // Check if command is session allowed
    if app
        .session_allowed_bash_commands
        .contains(&command.to_string())
    {
        return PermissionStatus::Allowed;
    }

    // Check if command is session denied
    if app
        .session_denied_bash_commands
        .contains(&command.to_string())
    {
        return PermissionStatus::Denied;
    }

    // Check if command is once denied
    if app.once_denied_bash_commands.contains(&command.to_string()) {
        return PermissionStatus::Denied;
    }

    // Prompt user for permission
    let options = vec![
        "Allow (once)",
        "Allow (session)",
        "Allow (always)",
        "Deny (once)",
        "Deny (session)",
        "Deny (always)",
    ];

    let message = format!("Bash command '{command}' requires permission:");
    let selection = Select::with_theme(&ColorfulTheme::default())
        .with_prompt(&message)
        .default(0)
        .items(&options)
        .interact_opt();
    let result = match selection {
        Ok(Some(index)) => match options[index] {
            "Allow (once)" => {
                app.once_denied_bash_commands.retain(|c| c != command);
                PermissionStatus::Allowed
            }
            "Allow (session)" => {
                app.session_allowed_bash_commands.push(command.to_string());
                PermissionStatus::Allowed
            }
            "Allow (always)" => {
                global_state.allowed_bash_commands.push(command.to_string());
                save_global_state(&global_state).unwrap();
                PermissionStatus::Allowed
            }
            "Deny (once)" => {
                app.once_denied_bash_commands.push(command.to_string());
                PermissionStatus::Denied
            }
            "Deny (session)" => {
                app.session_denied_bash_commands.push(command.to_string());
                PermissionStatus::Denied
            }
            "Deny (always)" => {
                global_state.denied_bash_commands.push(command.to_string());
                save_global_state(&global_state).unwrap();
                PermissionStatus::Denied
            }
            _ => panic!("Unknown option: {}", options[index]),
        },
        Ok(None) | Err(_) => {
            // User canceled the selection (e.g., pressed Esc or Ctrl-C)
            print_warning("Bash command execution cancelled by user.");
            PermissionStatus::Cancelled
        }
    };
    if result != PermissionStatus::Cancelled {
        println!();
    }
    result
}
