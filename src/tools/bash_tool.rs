use crate::display::print::{print_error, print_tool_request};
use crate::tools::utils::bash_permission::{check_bash_permission, PermissionStatus};
use crate::tools::utils::tool_result_truncation::truncate_tool_output_if_needed;
use crate::tools::utils::USER_CANCELLED_TOOL_EXECUTION;
use crate::utils::spinner::Spinner;
use crate::{app::App, config::constants::BASH_COMMANDS_TIMEOUT_SECONDS};
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::process::Command as TokioCommand;
use tokio::time::timeout;

pub async fn execute_bash_tool(command: &str, cwd: PathBuf, app_arc: Arc<Mutex<App>>) -> String {
    let yolo_mode = {
        let app = app_arc.lock().unwrap();
        app.cli_args.yolo
    }; // `app` (MutexGuard) is dropped here

    if yolo_mode {
        let spinner =
            Spinner::new(format!("Executing bash command: {command}. Ctrl+C to abort...").as_str());
        let tool_output_str =
            execute_shell_command(command, cwd, BASH_COMMANDS_TIMEOUT_SECONDS, &spinner).await;
        spinner.stop().await;
        println!(); // Add initial newline
        print_tool_request(format!("Executed bash command: {command}\n"));
        return tool_output_str;
    }

    let app_arc_clone = app_arc.clone();
    let command_clone = command.to_string();
    let permission_status =
        tokio::task::spawn_blocking(move || check_bash_permission(&app_arc_clone, &command_clone))
            .await
            .unwrap_or_else(|e| {
                print_error(format!("Permission check task failed: {e}"));
                PermissionStatus::Denied
            });

    if permission_status == PermissionStatus::Cancelled {
        return USER_CANCELLED_TOOL_EXECUTION.to_string();
    }

    if permission_status == PermissionStatus::Denied {
        return format!("Command execution denied by user: {command}");
    }

    let spinner =
        Spinner::new(format!("Executing bash command: {command}. Ctrl+C to abort...").as_str());
    let tool_output_str =
        execute_shell_command(command, cwd, BASH_COMMANDS_TIMEOUT_SECONDS, &spinner).await;
    spinner.stop().await;
    println!(); // Add initial newline
    print_tool_request(format!("Executed bash command: {command}\n"));
    tool_output_str
}

/// Executes a shell command, streams its stdout/stderr to the console,
/// and returns the collected output.
async fn execute_shell_command(
    command: &str,
    cwd: PathBuf,
    timeout_seconds: u64,
    spinner: &Spinner,
) -> String {
    let timeout_duration = Duration::from_secs(timeout_seconds);

    let mut cmd = TokioCommand::new("bash");
    cmd.arg("-c").arg(command);
    cmd.current_dir(&cwd);
    cmd.stdout(std::process::Stdio::piped());
    cmd.stderr(std::process::Stdio::piped());

    let mut child = match cmd.spawn() {
        Ok(child) => child,
        Err(e) => return format!("Failed to spawn command: {e}"),
    };

    let stdout = child.stdout.take().expect("Failed to open stdout");
    let stderr = child.stderr.take().expect("Failed to open stderr");

    let mut stdout_reader = BufReader::new(stdout).lines();
    let mut stderr_reader = BufReader::new(stderr).lines();

    let command_future = async {
        let mut output = String::new();
        let mut stdout_done = false;
        let mut stderr_done = false;

        while !stdout_done || !stderr_done {
            tokio::select! {
                biased;
                res = stdout_reader.next_line(), if !stdout_done => {
                    match res {
                        Ok(Some(line)) => {
                            spinner.hide();
                            println!("{line}");
                            output.push_str(&line);
                            output.push('\n');
                        },
                        Ok(None) => stdout_done = true,
                        Err(e) => {
                            let err_msg = format!("Error reading stdout: {e}");
                            spinner.hide();
                            println!("{err_msg}");
                            output.push_str(&err_msg);
                            output.push('\n');
                            stdout_done = true;
                        },
                    }
                },
                res = stderr_reader.next_line(), if !stderr_done => {
                    match res {
                        Ok(Some(line)) => {
                            spinner.hide();
                            println!("{line}");
                            output.push_str(&line);
                            output.push('\n');
                        },
                        Ok(None) => stderr_done = true,
                        Err(e) => {
                            let err_msg = format!("Error reading stderr: {e}");
                            spinner.hide();
                            println!("{err_msg}");
                            output.push_str(&err_msg);
                            output.push('\n');
                            stderr_done = true;
                        },
                    }
                },
            }
        }
        (child.wait().await, output)
    };

    match timeout(timeout_duration, command_future).await {
        Ok((Ok(status), mut output)) => {
            if status.success() {
                if output.trim().is_empty() {
                    output = "Command executed successfully with no output.".to_string();
                }
            } else {
                let exit_code = status.code().unwrap_or(-1);
                let final_message =
                    format!("\nCommand '{command}' failed with exit code {exit_code}");
                spinner.hide();
                println!("{final_message}");
                output.push_str(&final_message);
            }
            truncate_tool_output_if_needed(output.trim_end())
        }
        Ok((Err(e), output)) => {
            format!("Failed to get command status: {e}. Output so far:\n{output}")
        }
        Err(_) => {
            format!("Command '{command}' timed out after {timeout_seconds:.1} seconds")
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::tokens::token_estimator::estimate_tokens;
    use crate::tools::utils::tool_result_truncation::{MAX_TOKENS, TRUNCATE_TOKENS};

    #[tokio::test]
    async fn test_execute_shell_command() {
        // Test a normal command
        let spinner = Spinner::new("Test Spinner");
        let output =
            execute_shell_command("echo 'hello world'", PathBuf::from("/"), 10, &spinner).await;
        spinner.stop().await;
        println!("Normal command output:\n{output}");

        // Test a command that generates 1 MB of text
        let spinner = Spinner::new("Test Spinner");
        let large_output = execute_shell_command(
            "dd if=/dev/zero bs=1024 count=1024 | tr '\\0' 'y'",
            PathBuf::from("/"),
            10,
            &spinner,
        )
        .await;
        spinner.stop().await;

        println!("Large output:\n{large_output}");

        // Verify that the output was truncated (if necessary)
        assert!(estimate_tokens(&large_output) <= MAX_TOKENS + TRUNCATE_TOKENS * 2);
    }
}
