use crate::display::print::{print_and_truncate_tool_output, print_error, print_tool_request};
use std::fs;
use std::path::PathBuf;
use std::process::Command;

pub async fn execute_write_file_tool(
    file_path: &str,
    content: Option<&str>,
    overwrite: bool,
    cwd: PathBuf,
) -> Result<String, String> {
    let full_path = cwd.join(file_path);
    let file_existed = full_path.exists();

    if file_existed && !overwrite {
        let error_msg =
            format!("File {file_path} already exists. Set overwrite to true to overwrite it.");
        print_error(error_msg.clone());
        println!();
        return Err(error_msg);
    }

    let content_to_write = content.unwrap_or("");

    // Check if the new content is the same as the existing content
    if file_existed {
        match fs::read_to_string(&full_path) {
            Ok(existing_content) => {
                // Check if content is exactly the same
                if existing_content == content_to_write {
                    let error_msg = format!(
                        "File {file_path} already has the same content. No changes needed."
                    );
                    print_error(error_msg.clone());
                    println!();
                    return Err(error_msg);
                }

                // Check if only trailing newline differs
                let existing_trimmed = existing_content.trim_end_matches('\n');
                let new_trimmed = content_to_write.trim_end_matches('\n');
                if existing_trimmed == new_trimmed {
                    let error_msg = format!("File {file_path} content is the same except for trailing newline. No meaningful changes needed.");
                    print_error(error_msg.clone());
                    println!();
                    return Err(error_msg);
                }
            }
            Err(_) => {
                // If we can't read the existing file, proceed with writing
                // This could happen due to permissions or other issues
            }
        }
    }

    let temp_file_name = format!("{}.tmp", file_path.replace(['/', '\\'], "_"));
    let temp_file_path = cwd.join(&temp_file_name);
    if let Err(e) = fs::write(&temp_file_path, content_to_write) {
        let error_msg = format!("Failed to write to temporary file: {e}");
        print_error(error_msg.clone());
        println!();
        let _ = fs::remove_file(&temp_file_path);
        return Err(error_msg);
    };

    // Run git diff from the workspace root with relative paths to get clean output
    let run_diff = |color_option: &str| {
        Command::new("git")
            .current_dir(&cwd)
            .arg("diff")
            .arg("--no-index")
            .arg(format!("--color={color_option}"))
            .arg(if file_existed { file_path } else { "/dev/null" })
            .arg(&temp_file_name)
            .output()
    };

    let colored_output_res = run_diff("always");
    let uncolored_output_res = run_diff("never");

    let _ = fs::remove_file(&temp_file_path);

    match fs::write(&full_path, content_to_write) {
        Ok(_) => {
            let msg = if file_existed {
                format!("Successfully overwrote {file_path}")
            } else {
                format!("Successfully created {file_path}")
            };

            match (colored_output_res, uncolored_output_res) {
                (Ok(colored_output), Ok(uncolored_output)) => {
                    let clean_diff = |bytes: &[u8]| {
                        String::from_utf8_lossy(bytes)
                            .lines()
                            .skip(4)
                            .collect::<Vec<_>>()
                            .join("\n")
                            .replace(&temp_file_name, file_path)
                    };

                    let colored_diff = clean_diff(&colored_output.stdout);
                    let uncolored_diff = clean_diff(&uncolored_output.stdout);

                    print_tool_request(&msg);
                    println!("\n{colored_diff}\n");
                    Ok(format!("{msg}\n\n{uncolored_diff}"))
                }
                (Err(e), _) | (_, Err(e)) => {
                    let error_msg = format!("Failed to generate diff: {e}");
                    print_error(error_msg.clone());
                    // Proceed without diff if there's an error generating it
                    print_tool_request(&msg);
                    println!();
                    print_and_truncate_tool_output(content_to_write);
                    Ok(msg)
                }
            }
        }
        Err(e) => {
            let error_msg = format!("Failed to write to file {file_path}: {e}");
            print_error(error_msg.clone());
            println!();
            Err(error_msg)
        }
    }
}
