use crate::display::print::{print_error, print_tool_request};
use std::fs;
use std::path::PathBuf;

pub async fn execute_delete_file_tool(file_path: &str, cwd: PathBuf) -> Result<String, String> {
    let full_path = cwd.join(file_path);
    if !full_path.exists() {
        let error_msg = format!("File {file_path} does not exist.");
        print_error(error_msg.clone());
        println!();
        return Err(error_msg);
    }
    match fs::remove_file(&full_path) {
        Ok(_) => {
            let msg = format!("Successfully deleted {file_path}");
            print_tool_request(msg.clone());
            println!();
            Ok(msg)
        }
        Err(e) => {
            let error_msg = format!("Failed to delete file {file_path}: {e}");
            print_error(error_msg.clone());
            println!();
            Err(error_msg)
        }
    }
}
