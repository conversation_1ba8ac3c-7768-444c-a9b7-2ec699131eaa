use crate::app::App;
use crate::config::constants::BASH_COMMANDS_TIMEOUT_SECONDS;
use crate::display::print::{print_error, print_warning};
use crate::tools::utils::tool_print_truncation::truncate_tool_output_for_print;
use crate::utils::spinner::Spinner;
use std::process::Stdio;
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::io::AsyncReadExt;
use tokio::process::Command as TokioCommand;
use tokio::signal;
use tokio::time::timeout;

pub async fn run_lint_command_if_needed(app_arc: &Arc<Mutex<App>>) -> Option<String> {
    let (lint_command, cwd) = {
        let mut app = app_arc.lock().unwrap();
        if !app.code_was_changed() {
            return None;
        }
        let lint_command = app
            .dima_config
            .as_ref()
            .and_then(|c| c.lint_command.clone());

        // Reset flag *before* running command to avoid re-running on Ctrl-C.
        app.reset_code_changed_flag();

        (lint_command, app.working_dir.clone())
    };

    if let Some(command) = lint_command {
        if command.is_empty() {
            return None;
        }

        println!(); // Add some space before the lint command output.
        let spinner_text = format!("Linting: {command}... (Ctrl-C to abort)");
        let spinner = Spinner::new(&spinner_text);

        let mut bash_cmd = TokioCommand::new("bash");
        bash_cmd
            .args(["-c", &command])
            .current_dir(cwd)
            .stdout(Stdio::piped())
            .stderr(Stdio::piped());

        let mut child = match bash_cmd.spawn() {
            Ok(child) => child,
            Err(e) => {
                spinner.stop().await;
                print_error(format!("Failed to spawn lint command '{command}': {e}\n"));
                return Some(format!("Failed to spawn lint command '{command}': {e}"));
            }
        };

        let mut stdout = child.stdout.take().expect("child stdout is piped");
        let mut stderr = child.stderr.take().expect("child stderr is piped");

        let stdout_reader = tokio::spawn(async move {
            let mut buffer = Vec::new();
            let _ = stdout.read_to_end(&mut buffer).await;
            buffer
        });

        let stderr_reader = tokio::spawn(async move {
            let mut buffer = Vec::new();
            let _ = stderr.read_to_end(&mut buffer).await;
            buffer
        });

        let timeout_duration = Duration::from_secs(BASH_COMMANDS_TIMEOUT_SECONDS);

        enum LintResult {
            Output(std::process::Output),
            Aborted,
            Timeout,
            ExecutionError(std::io::Error),
        }

        let result = tokio::select! {
            biased;
            _ = signal::ctrl_c() => {
                // Ignore the result, we are aborting.
                let _ = child.kill().await;
                LintResult::Aborted
            },
            res = timeout(timeout_duration, child.wait()) => {
                match res {
                    Ok(Ok(status)) => {
                        let stdout = stdout_reader.await.unwrap_or_default();
                        let stderr = stderr_reader.await.unwrap_or_default();
                        LintResult::Output(std::process::Output {
                            status,
                            stdout,
                            stderr,
                        })
                    },
                    Ok(Err(e)) => LintResult::ExecutionError(e),
                    Err(_) => LintResult::Timeout,
                }
            }
        };
        spinner.stop().await;

        match result {
            LintResult::Aborted => {
                print_warning("Lint command aborted by user.");
                None
            }
            LintResult::Output(output) => {
                if output.status.success() {
                    None
                } else {
                    let stdout = String::from_utf8_lossy(&output.stdout);
                    let stderr = String::from_utf8_lossy(&output.stderr);

                    if stdout.is_empty() && stderr.is_empty() {
                        print_error(format!("Lint command '{command}' failed with no output.\n"));
                        return Some(format!("Lint command '{command}' failed with no output."));
                    }

                    let output_message = if !stdout.is_empty() && !stderr.is_empty() {
                        format!("--- stdout ---\n{stdout}\n--- stderr ---\n{stderr}")
                    } else if !stdout.is_empty() {
                        format!("--- stdout ---\n{stdout}")
                    } else {
                        format!("--- stderr ---\n{stderr}")
                    };

                    let formatted_output = truncate_tool_output_for_print(&output_message);
                    print_error(format!(
                        "Lint command '{command}' failed.\n{formatted_output}\n"
                    ));
                    Some(format!(
                        "Lint command '{command}' failed.\n{output_message}"
                    ))
                }
            }
            LintResult::ExecutionError(e) => {
                print_error(format!("Failed to execute lint command '{command}': {e}\n"));
                Some(format!("Failed to execute lint command '{command}': {e}"))
            }
            LintResult::Timeout => {
                print_error(format!(
                    "Lint command '{command}' timed out after {BASH_COMMANDS_TIMEOUT_SECONDS} seconds\n"
                ));
                Some(format!(
                    "Lint command '{command}' timed out after {BASH_COMMANDS_TIMEOUT_SECONDS} seconds"
                ))
            }
        }
    } else {
        None
    }
}
