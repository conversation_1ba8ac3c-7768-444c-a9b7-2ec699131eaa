#!/usr/bin/env python3

# Read the original file
with open('llm/src/lib.rs', 'r') as f:
    lines = f.readlines()

# Find the line with the closing brace of relogin_github_copilot method
insert_index = None
for i, line in enumerate(lines):
    if line.strip() == '}' and i > 0:
        prev_lines = ''.join(lines[max(0, i-5):i])
        if 'relogin_github_copilot' in prev_lines and 'This provider does not support GitHub Copilot re-login' in prev_lines:
            insert_index = i + 1
            break

if insert_index is None:
    print("Could not find insertion point")
    exit(1)

# Insert the new method
new_lines = [
    "\n",
    "    fn get_github_copilot_usage(&self) -> Result<CopilotUsageInfo, crate::error::LLMError> {\n",
    "        Err(crate::error::LLMError::ProviderError(\n",
    "            \"This provider does not support GitHub Copilot usage reporting.\".to_string(),\n",
    "        ))\n",
    "    }\n"
]

# Insert the new lines
lines[insert_index:insert_index] = new_lines

# Write the modified file
with open('llm/src/lib.rs', 'w') as f:
    f.writelines(lines)

print("Successfully added get_github_copilot_usage method")
