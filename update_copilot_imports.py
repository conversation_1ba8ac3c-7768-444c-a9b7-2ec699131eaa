#!/usr/bin/env python3

# Read the original file
with open('llm/src/backends/copilot.rs', 'r') as f:
    lines = f.readlines()

# Find the chrono import line and update it
for i, line in enumerate(lines):
    if 'use chrono::Utc;' in line:
        lines[i] = 'use chrono::{DateTime, Local, Utc};\n'
        break

# Write the modified file
with open('llm/src/backends/copilot.rs', 'w') as f:
    f.writelines(lines)

print("Successfully updated chrono imports")
