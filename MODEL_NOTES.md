# Notes

Note that my implementation has smart API key cycling, so if you have multiple `GOOGLE_GEMINI_API_KEY` keys,
read `src/provider_models/config/google_gemini.rs` and related files on the environment variable names to use for the
different keys.

All tests below are for `OnlyWriteFile` if not otherwise specified.

# GitHub Copilot (✅)

The free tier has 500 free requests every month across many models, including Claude 3.5 Sonnet.

You can use the `reauth-github-copilot` command to switch to a different account for more access.

Not sure if only Claude 3.5 Sonnet is so slow, maybe the other models are quicker.

## Claude 3.5 Sonnet (✅ good, but slow)

It is really slow to respond. It might be intentionally throttled.

# Google (✨)

Requires the `GOOGLE_GEMINI_API_KEY` environment variable to be set.

If the Flash models start asking too many questions, just tell them to stop and finish the task.

## Gemini 2.5 Flash (✨ excellent)

This is pretty good.

## Gemini 2.0 Flash (✅ fine)

Way worse than Gemini 2.5 Flash actually, but still alright.

# Mistral Codestral (✅ a bit slow, but okay)

Not as good as the Google Gemini models, might be on the 2.0 Flash level for some tasks.

I tested with the Gemini system prompt, and I think it is just too complex for it, it performs worse that way.

# Cohere

## Command R-08 2024 (❌too slow)

It is so slow, that it is no fun to use.

Can fail with 422 with this error which goes away on retrying.

```json
{
  "id": "********-5921-4b6e-bc84-9c1aaa7f0674",
  "message": "your request resulted in an invalid tool generation. Try updating the messages or tool definitions"
}
```

It keeps getting 'stuck' by writing `I will now ...`, but forgets to call tools, so the loop is broken.

# OpenRouter

## deepseek/deepseek-r1-0528:free, qwen3-235b-a22b:free

Do not support tool use, so it is not included.

## Mistral 3.1 24B Instruct / Mistral 3.2 24B Instruct

Just broken, always fail with this, although 200 it returns a status code.

Other free models like `deepseek/deepseek-chat:free` work just fine, although they have no tool support.

```json
{
  "error": {
    "message": "Internal Server Error",
    "code": 500
  },
  "user_id": "user_2towirRZERkEs9tld9TtQCTZNHb"
}
```

# gpt4free (❌ not recommended for tool use)

https://github.com/xtekky/gpt4free

I am not able to find a stable model + provider that provides tool use.

This repo has tested all interesting models, and there are only 3 that provide tools, and those are unreliable, as well.
Without tool use, there are good models though, like o3, deepseek, grok models.

https://github.com/Dima-369/gpt4free-python-ai-llm-model-provider-tester-system-tools

# Pollinations.AI (❌ unreliable)

Often has network issues which go away on retrying, sometimes on retrying a lot.

On requesting o3, DeepSeek, grok or GPT-4.1 models, very often, the model is instead just `gpt-4.1-nano-2025-04-14`.

Interestingly, gpt4free also uses Pollinations.AI, and receives the real models, not always `gpt-4.1-nano-2025-04-14`.

## GPT-4.1 nano (🤨 okay)

Surprisingly, it can perform fine, on editing whole files sometimes, but often times it just messes them up.
It is really nice though, when it just needs to run some bash commands, since it responds quick.

It often messes up the `find_and_replace_code` tool and does not even understand it, when the tool reports failure.

# Google Vertex.AI (❌ token expires too often)

Very often, the token behind `VERTEX_AI_AUTHORIZATION` expires and needs to be refreshed,
which is super annoying. I think every hour?

You need to run `gcloud auth print-access-token` to get a new token. I do it in the Cloud Shell.
Maybe there is a better way?

## Deepseek-R1-0528 (❌ tool uses fail)

As soon as any tool responses are used in the request body, it fails with `500: Internal error encountered.`,
rendering this useless without `--aider`.

Also, the response body does not differentiate between `content` and `reasoning_content`.
The chain of thought (COT) is not supposed to be appended to the assistant messages, but it is impossible to distinguish
between the two reliably because the COT is not in a separate message.

# Kluster.ai

https://platform.kluster.ai/plans

Has those free limits making it impractical for many tasks because the context window is too small,
even if the model is good.

- Requests per minute: 30
- Context window: Up to 32K tokens
- Max output: Up to 4K tokens

# Inference.net

https://inference.net/

Free credits: $1, $25 on responding to an email survey

Does not have any interesting models for this use-case.