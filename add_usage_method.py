#!/usr/bin/env python3

# Read the original file
with open('llm/src/backends/copilot.rs', 'r') as f:
    lines = f.readlines()

# Find the closing brace of relogin_github_copilot method
insert_index = None
for i, line in enumerate(lines):
    if line.strip() == '}' and i > 0:
        prev_lines = ''.join(lines[max(0, i-5):i])
        if 'relogin_github_copilot' in prev_lines and 'Ok(())' in prev_lines:
            insert_index = i + 1
            break

if insert_index is None:
    print("Could not find insertion point")
    exit(1)

# Insert the new method
new_lines = [
    "\n",
    "    fn get_github_copilot_usage(&self) -> Result<crate::CopilotUsageInfo, crate::error::LLMError> {\n",
    "        tokio::task::block_in_place(|| {\n",
    "            tokio::runtime::Handle::current().block_on(self.fetch_copilot_usage())\n",
    "        })\n",
    "    }\n"
]

# Insert the new lines
lines[insert_index:insert_index] = new_lines

# Write the modified file
with open('llm/src/backends/copilot.rs', 'w') as f:
    f.writelines(lines)

print("Successfully added get_github_copilot_usage method")
